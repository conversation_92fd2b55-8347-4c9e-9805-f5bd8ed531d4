package com.center.emergency.biz.robot.service;

import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.files.service.FileComponent;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.mcp.persistence.QMcpModel;
import com.center.emergency.biz.robot.DTO.KnowledgeBaseDTO;
import com.center.emergency.biz.robot.factory.AnswerStrategyFactory;
import com.center.emergency.biz.robot.persitence.*;
import com.center.emergency.biz.robot.pojo.*;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.pojo.IdAndValue;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import com.center.infrastructure.system.biz.depart.service.DepartServiceImpl;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class RobotServiceImpl implements RobotService {

    @Autowired
    private RobotRepository robotRepository;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private RobotKBRepository robotKBRepository;

    @Resource
    private DepartServiceImpl departServiceImpl;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private AnswerStrategyFactory answerStrategyFactory;

    @Resource
    private FileComponent fileComponent;


    @Override
    @Transactional
    public Long createRobots(AssistantCreateReq req) {
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        //1.检查智能助手名称是否重复
        if (robotRepository.existsByRobotNameAndTenantId(req.getRobotName(), tenantId)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "机器人名称重复");
        }
        //2.创建智能助手并设置默认值
        RobotModel robotModel = OrikaUtils.convert(req, RobotModel.class);
        robotModel.setRobotStatus(CommonStatusEnum.ACTIVE);

        //3. 保存实体并获取生成的id
        RobotModel saveRobot = robotRepository.save(robotModel);
        Long robotId = saveRobot.getId();

        //4. 创建关联知识库、MCP、数据库、文件
        answerStrategyFactory.getAnswerStrategyService(req.getAnswerStrategy()).createRobotAnswerStrategy(robotId,req.getAnswerStrategy());
        return robotId;
    }


    @Override
    @Transactional
    public void updateRobot(AssistantUpdateReq req) {
        //1. 判断智能助手是否存在
        RobotModel robotModel = robotRepository.findById(req.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在"));

        //2. 更新智能助手名称和备注
        OrikaUtils.copy(req, robotModel);
        robotRepository.save(robotModel);
    }




    @Override    //分页查询所有机器人方法
    public PageResult<RobotPageResp> getRobotsByPage(RobotPageReq pageReq) {
        Pageable pageable = PageRequest.of(pageReq.getPageNo() - 1, pageReq.getPageSize());  // 创建分页请求，page从0开始
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();

        QRobotModel qRobotModel = QRobotModel.robotModel;
        QUserModel qUserModel = QUserModel.userModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;

        BooleanBuilder whereBuilder = new BooleanBuilder();
        whereBuilder.and(qRobotModel.tenantId.eq(tenantId));

        //2. robotName 不为空时，根据名称进行模糊查询；否则全部查询
        if (StrUtil.isNotEmpty(pageReq.getName())) {
            whereBuilder.and(qRobotModel.robotName.contains(pageReq.getName()));
        }

        // 2.1 获取查询结果 - 使用左连接获取机器人的最后使用时间（最后回答时间），按最后使用时间排序
        JPQLQuery jpqlQuery = jpaQueryFactory.select((Projections.bean(
                        RobotPageResp.class,
                        qRobotModel.id,
                        qRobotModel.robotName,
                        qRobotModel.robotStatus,
                        qRobotModel.createTime,
                        qRobotModel.remark,
                        qRobotKnowledgeModel.answerStrategy,
                        qUserModel.displayName)))
                .from(qRobotModel)
                .join(qUserModel).on(qRobotModel.updaterId.eq(qUserModel.id))
                .leftJoin(qRobotKnowledgeModel).on(qRobotModel.id.eq(qRobotKnowledgeModel.robotId))
                .leftJoin(qChatSessionModel).on(
                    qChatSessionModel.robotId.eq(qRobotModel.id)
                    .and(qChatSessionModel.creatorId.eq(userId)) // 只关联当前用户的会话
                )
                .leftJoin(qChatAnswerModel).on(
                    qChatAnswerModel.sessionId.eq(qChatSessionModel.id) // 关联回答表，获取当前用户的回答
                )
                .where(whereBuilder)
                .groupBy(qRobotModel.id, qRobotModel.robotName, qRobotModel.robotStatus, qRobotModel.createTime,
                        qRobotModel.remark, qRobotKnowledgeModel.answerStrategy, qUserModel.displayName)
                .orderBy(
                    // 按当前用户最后回答时间倒序，如果没有回答则使用机器人创建时间
                    qChatAnswerModel.createTime.max().coalesce(qRobotModel.createTime).desc()
                )
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()) // 设置每页记录数
                .distinct();

        Long total = jpqlQuery.fetchCount();
        List<RobotPageResp> resultList = jpqlQuery.fetch();

        // 2.2 构建分页响应并返回
        return PageResult.of(resultList, total);
    }


    @Override
    @Transactional
    public void saveRobot(RobotSaveReq req) {
        // 1. 根据ID查找机器人
        Long robotId = req.getId();
        String robotName = robotRepository.findById(robotId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在")).getRobotName();

        //1.1 判断更新的机器人名称是否发生变化，如果变化则判重并更新；否则不判重
        if (!robotName.equals(req.getRobotName()) && robotRepository.existsByRobotNameAndTenantId(req.getRobotName(), LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "机器人名称重复");
        }

        // 2. 更新机器人表数据
        RobotModel updateRobot = OrikaUtils.convert(req, RobotModel.class);
        // 特殊处理 dialogueExamples
        if (CollectionUtils.isNotEmpty(req.getDialogueExamples())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String dialogueJson = objectMapper.writeValueAsString(req.getDialogueExamples());
                updateRobot.setDialogueExamples(dialogueJson);
            } catch (JsonProcessingException e) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "对话示例转换失败");
            }
        }

        // 3. 保存机器人更新后的数据
        robotRepository.save(updateRobot);

        // 4. 根据传入的知识库id列表修改 机器人-关联表的信息(MCP,KB,数据库)
        answerStrategyFactory.getAnswerStrategyService(req.getAnswerStrategy()).updateRobotAnswerStrategy(req.getId(),req.getAnswerStrategy(),req.getAnswerStrategyIds());
    }


    @Override
    public void switchStatus(RobotStatusReq statusReq) {
        // 1. 根据id查找机器人
        RobotModel robot = robotRepository.findById(statusReq.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在"));
        // 2. 切换状态
        robot.setRobotStatus(CommonStatusEnum.valueOf(statusReq.getRobotStatus()));
        // 3. 保存更改
        robotRepository.save(robot);
    }


    /**
     * 获取部门及其知识库列表
     * <p>
     * 此方法用于获取指定部门及其关联知识库的列表信息它首先获取当前登录用户的部门和租户ID，
     * 然后调用服务层方法获取初始部门列表随后，根据部门ID是否在特定检查列表中，决定是获取用户所在部门的知识库列表，
     * 还是指定部门的知识库列表最后，将知识库列表合并到结果中并返回
     *
     * @param departId 部门ID，用于指定需要获取信息的部门
     * @param path     路径信息
     * @return 返回一个包含部门及其知识库信息的列表
     */
    @Override
    public List<DepartAndKbListResp> listDepartAndKb(Long departId, String path) {
        // 获取当前登录用户的部门ID
        Long userDepartId = LoginContextHolder.getLoginUserDepartId();
        if (userDepartId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "获取登录用户部门ID失败");
        }
        // 获取当前登录用户的租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (tenantId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "获取登录用户租户ID失败");
        }
        // 调用服务层方法，获取部门列表信息
        List<DepartAndKbListResp> result = departServiceImpl.listDeparts(departId, path, true);
        //如果departId不为空
        if(departId!=null){
            result.addAll(listKbs(departId));
        }

        // 返回合并后的结果列表
        return result;
    }


    /**
     * 根据部门ID获取知识库列表
     * 此方法首先从数据库中查询与部门ID关联的所有知识库模型，
     * 然后构建并返回一个包含这些知识库的树形结构列表
     *
     * @param departId 部门ID，用于查询知识库
     * @return 返回一个构建好的知识库树形结构列表
     */
    public List<DepartAndKbListResp> listKbs(Long departId) {

        // 构建并返回知识库树形结构列表
        List<KnowledgeBaseModel>  knowledgeBaseModelList = knowledgeBaseRepository.findByDepartmentId(departId);
        return buildKbTree(knowledgeBaseModelList);
    }

    /**
     * 构建知识库树结构
     * 此方法的目的是将给定的知识库模型列表转换为树形结构，以便于在前端展示或进一步处理
     * 它通过创建一个映射来跟踪每个知识库模型，并将其关系构建为树形结构
     *
     * @param knowledgeBaseModelList 知识库模型列表，用于构建树形结构如果列表为空或为null，方法将返回一个空列表
     * @return 返回一个列表，包含构建好的知识库树结构
     */
    private List<DepartAndKbListResp> buildKbTree(List<KnowledgeBaseModel> knowledgeBaseModelList) {
        List<DepartAndKbListResp> result = new ArrayList<>();
        if (null != knowledgeBaseModelList) {
            for (KnowledgeBaseModel knowledgeBaseModel : knowledgeBaseModelList) {
                DepartAndKbListResp departAndKbListResp =
                        new DepartAndKbListResp(knowledgeBaseModel.getId(), knowledgeBaseModel.getKbName(), null, false, Collections.emptyList());
                result.add(departAndKbListResp);
            }
        }
        return result;
    }
    @Override
    public RobotDetailsResp getRobotDetails(Long id) {
        // 获取机器人信息
        RobotModel robotModel = robotRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                        String.format("机器人不存在,请刷新页面后重试")));
        // 转换为返回对象
        RobotDetailsResp robotDetailsResp = OrikaUtils.convert(robotModel, RobotDetailsResp.class);
//        设置Agent的问答策略和策略列表信息
        RobotKnowledgeModel robotKnowledgeModel = robotKBRepository.findFirstByRobotId(id);
        if (robotKnowledgeModel != null){
            List<Long> strategyList = answerStrategyFactory.getAnswerStrategyService(robotKnowledgeModel.getAnswerStrategy()).listAnswerStrategyIds(id);
            robotDetailsResp.setAnswerStrategyIds(strategyList);
            robotDetailsResp.setAnswerStrategy(robotKnowledgeModel.getAnswerStrategy());
        }
        return robotDetailsResp;
    }

    @Override
    public List<MCPResp> getMCPInfoByRobotId(Long id) {
        //1. 先检查agent是否存在
        RobotModel robot = robotRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在"));

        //2. 从关联表查找符合条件的MCP信息
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QMcpModel qMcpModel = QMcpModel.mcpModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(id));
        builder.and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.MCP));

        List<MCPResp> mcpRespList = jpaQueryFactory.select(Projections.bean(
                MCPResp.class,
                qMcpModel.id.as("mcpId"),
                qMcpModel.logoId,
                qMcpModel.mcpName))
                .from(qRobotKnowledgeModel)
                .join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id))
                .where(builder)
                .fetch();

        for (MCPResp mcpResp: mcpRespList){
            if (mcpResp.getLogoId() != null){
                mcpResp.setPreviewUrl(fileComponent.previewUrlByIdForLogo(mcpResp.getLogoId()));
            }
        }

        return mcpRespList;
    }


}
